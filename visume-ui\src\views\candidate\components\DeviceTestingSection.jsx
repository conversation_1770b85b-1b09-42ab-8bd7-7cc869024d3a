import React, { useEffect, useState } from "react";
import VideoPreview from "../smaller_comp/VideoPreview";
import StatusIndicator from "../smaller_comp/StatusIndicator";
import Button from "../smaller_comp/Button";
import { Mic, Camera, Shield, Volume2, Play, CheckCircle, AlertCircle } from "lucide-react";

const DeviceTestingSection = ({ localCamStream, startWebcam, onStartInterview }) => {
  const [devices, setDevices] = useState({ cameras: [], microphones: [] });
  const [selectedCamera, setSelectedCamera] = useState("");
  const [selectedMicrophone, setSelectedMicrophone] = useState("");
  const [volumeLevel, setVolumeLevel] = useState(0); // Store the current volume level
  const [micColor, setMicColor] = useState("gray"); // Initial color (gray when inactive)

  useEffect(() => {
    // Function to list media devices
    const getMediaDevices = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const cameras = devices.filter((device) => device.kind === "videoinput");
      const microphones = devices.filter(
        (device) => device.kind === "audioinput"
      );
      setDevices({ cameras, microphones });

      // Set default camera and microphone
      if (cameras.length > 0) setSelectedCamera(cameras[0].deviceId);
      if (microphones.length > 0)
        setSelectedMicrophone(microphones[0].deviceId);
    };

    getMediaDevices();
    startWebcam(); // Automatically starts the webcam when component mounts
  }, []);

  useEffect(() => {
    if (!selectedMicrophone) return;

    const startAudioStream = async () => {
      try {
        // Access the selected microphone
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: { deviceId: selectedMicrophone },
        });

        // Create an audio context and analyser node for volume detection
        const audioContext = new (window.AudioContext ||
          window.webkitAudioContext)();
        const analyser = audioContext.createAnalyser();
        const microphoneSource = audioContext.createMediaStreamSource(stream);

        microphoneSource.connect(analyser);
        analyser.fftSize = 256; // FFT size to define frequency resolution
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const updateVolume = () => {
          analyser.getByteFrequencyData(dataArray);

          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            sum += dataArray[i];
          }

          const average = sum / dataArray.length;
          setVolumeLevel(average);

          // Set the microphone icon color based on volume level
          if (average < 30) {
            setMicColor("yellow"); // Low volume
          } else if (average < 60) {
            setMicColor("orange"); // Medium volume
          } else {
            setMicColor("red"); // Very high volume
          }

          // Continue to analyze the audio
          requestAnimationFrame(updateVolume);
        };

        // Start volume monitoring
        updateVolume();
      } catch (error) {
        console.error("Error accessing microphone", error);
      }
    };

    startAudioStream();

  }, [selectedMicrophone]); // Restart when the microphone is selected

  const handleCameraChange = (e) => {
    setSelectedCamera(e.target.value);
    startWebcam(e.target.value); // Pass the selected camera ID to start the webcam
  };

  const handleMicrophoneChange = (e) => {
    setSelectedMicrophone(e.target.value);
    // Implement logic to change microphone input
  };

  return (
    <div className="flex w-full h-full overflow-hidden items-center justify-center">
      {/* Left side - Video Preview (Google Meet style) */}
        <div className="w-full max-w-2xl">
          <VideoPreview stream={localCamStream} />
        </div>

      {/* Right side - Controls Panel (Google Meet style) */}
      <div className="p-10 flex flex-col">
        <div className="flex-1">
          {/* Ready to join section */}
          <div className="">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Ready to start?</h2>
            
            <button
              onClick={onStartInterview}
              disabled={!localCamStream}
              className={`w-full mb-4 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                localCamStream
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              <Play className="w-4 h-4 inline mr-2" />
              Start Interview
            </button>
          </div>

          {/* Device Settings */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Device Settings</h3>
            
            {/* Camera Selection */}
            <div className="mb-3">
              <label htmlFor="camera-select" className="block text-xs font-medium text-gray-700 mb-1">
                Camera
              </label>
              <select
                id="camera-select"
                value={selectedCamera}
                onChange={handleCameraChange}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {devices.cameras.map((camera) => (
                  <option key={camera.deviceId} value={camera.deviceId}>
                    {camera.label || `Camera ${camera.deviceId}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Microphone Selection */}
            <div className="mb-3">
              <label htmlFor="microphone-select" className="block text-xs font-medium text-gray-700 mb-1">
                Microphone
              </label>
              <select
                id="microphone-select"
                value={selectedMicrophone}
                onChange={handleMicrophoneChange}
                className="w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {devices.microphones.map((mic) => (
                  <option key={mic.deviceId} value={mic.deviceId}>
                    {mic.label || `Microphone ${mic.deviceId}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Audio Level Indicator */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Volume2 className="w-4 h-4 text-gray-600" />
                <span className="text-xs font-medium text-gray-700">Audio Test</span>
              </div>
              <div className="flex items-center gap-3">
                <Mic
                  className={`h-5 w-5 transition-colors duration-300 ${
                    micColor === "gray" ? "text-gray-400" : ""
                  } ${micColor === "yellow" ? "text-yellow-400" : ""} ${
                    micColor === "orange" ? "text-orange-400" : ""
                  } ${micColor === "red" ? "text-red-500" : ""}`}
                />
                <div className="flex-1">
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-blue-500 h-1.5 rounded-full transition-all duration-200"
                      style={{ width: `${Math.min(volumeLevel * 2, 100)}%` }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {volumeLevel < 30 ? "Speak louder" : volumeLevel < 60 ? "Good level" : "Perfect!"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">System Status</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {localCamStream ? (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                ) : (
                  <AlertCircle className="w-3 h-3 text-red-500" />
                )}
                <span className={`text-xs ${localCamStream ? 'text-green-700' : 'text-red-700'}`}>
                  Camera & Mic Access
                </span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span className="text-xs text-green-700">
                  Recording Ready
                </span>
              </div>
            </div>
          </div>
          
          {/* Quick Guidelines */}
          <div className="text-xs text-gray-600 space-y-1">
            <div>• Find a quiet, well-lit space</div>
            <div>• Keep your face visible</div>
            <div>• Interview will be in fullscreen</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceTestingSection;
