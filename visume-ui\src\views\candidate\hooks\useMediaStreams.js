import { useState, useCallback } from "react";
import toast from "react-hot-toast";

export function useMediaStreams() {
  const [localCamStream, setLocalCamStream] = useState(null);


  const startWebcam = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        },
      });

      // Check if the stream contains both video and audio tracks
      const videoTrack = stream.getVideoTracks()[0];
      const audioTrack = stream.getAudioTracks()[0];

      if (!videoTrack || !audioTrack) {
        stream.getTracks().forEach((track) => track.stop());
        return toast.error("Both webcam and microphone must be shared to start the interview.");
      }
      setLocalCamStream(stream);
    } catch (err) {
      console.error("Error starting webcam and audio: ", err);
      toast.error("There was an error accessing your camera or microphone.");

    }
  }, []);

  const stopAllStreams = useCallback(() => {

    if (localCamStream) {
      localCamStream.getTracks().forEach((track) => track.stop());
      setLocalCamStream(null);
    }
  }, [localCamStream]);

  return {
    localCamStream,
    startWebcam,
    stopAllStreams,
  };
}
